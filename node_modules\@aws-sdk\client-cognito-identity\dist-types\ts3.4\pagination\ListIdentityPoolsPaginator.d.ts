import { Paginator } from "@smithy/types";
import {
  ListIdentityPoolsCommandInput,
  ListIdentityPoolsCommandOutput,
} from "../commands/ListIdentityPoolsCommand";
import { CognitoIdentityPaginationConfiguration } from "./Interfaces";
export declare const paginateListIdentityPools: (
  config: CognitoIdentityPaginationConfiguration,
  input: ListIdentityPoolsCommandInput,
  ...rest: any[]
) => Paginator<ListIdentityPoolsCommandOutput>;
