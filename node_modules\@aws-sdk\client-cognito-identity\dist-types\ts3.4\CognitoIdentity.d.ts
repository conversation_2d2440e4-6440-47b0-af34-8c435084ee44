import { HttpHandlerOptions as __HttpHandlerOptions } from "@smithy/types";
import { CognitoIdentityClient } from "./CognitoIdentityClient";
import {
  CreateIdentityPoolCommandInput,
  CreateIdentityPoolCommandOutput,
} from "./commands/CreateIdentityPoolCommand";
import {
  DeleteIdentitiesCommandInput,
  DeleteIdentitiesCommandOutput,
} from "./commands/DeleteIdentitiesCommand";
import {
  DeleteIdentityPoolCommandInput,
  DeleteIdentityPoolCommandOutput,
} from "./commands/DeleteIdentityPoolCommand";
import {
  DescribeIdentityCommandInput,
  DescribeIdentityCommandOutput,
} from "./commands/DescribeIdentityCommand";
import {
  DescribeIdentityPoolCommandInput,
  DescribeIdentityPoolCommandOutput,
} from "./commands/DescribeIdentityPoolCommand";
import {
  GetCredentialsForIdentityCommandInput,
  GetCredentialsForIdentityCommandOutput,
} from "./commands/GetCredentialsForIdentityCommand";
import { GetIdCommandInput, GetIdCommandOutput } from "./commands/GetIdCommand";
import {
  GetIdentityPoolRolesCommandInput,
  GetIdentityPoolRolesCommandOutput,
} from "./commands/GetIdentityPoolRolesCommand";
import {
  GetOpenIdTokenCommandInput,
  GetOpenIdTokenCommandOutput,
} from "./commands/GetOpenIdTokenCommand";
import {
  GetOpenIdTokenForDeveloperIdentityCommandInput,
  GetOpenIdTokenForDeveloperIdentityCommandOutput,
} from "./commands/GetOpenIdTokenForDeveloperIdentityCommand";
import {
  GetPrincipalTagAttributeMapCommandInput,
  GetPrincipalTagAttributeMapCommandOutput,
} from "./commands/GetPrincipalTagAttributeMapCommand";
import {
  ListIdentitiesCommandInput,
  ListIdentitiesCommandOutput,
} from "./commands/ListIdentitiesCommand";
import {
  ListIdentityPoolsCommandInput,
  ListIdentityPoolsCommandOutput,
} from "./commands/ListIdentityPoolsCommand";
import {
  ListTagsForResourceCommandInput,
  ListTagsForResourceCommandOutput,
} from "./commands/ListTagsForResourceCommand";
import {
  LookupDeveloperIdentityCommandInput,
  LookupDeveloperIdentityCommandOutput,
} from "./commands/LookupDeveloperIdentityCommand";
import {
  MergeDeveloperIdentitiesCommandInput,
  MergeDeveloperIdentitiesCommandOutput,
} from "./commands/MergeDeveloperIdentitiesCommand";
import {
  SetIdentityPoolRolesCommandInput,
  SetIdentityPoolRolesCommandOutput,
} from "./commands/SetIdentityPoolRolesCommand";
import {
  SetPrincipalTagAttributeMapCommandInput,
  SetPrincipalTagAttributeMapCommandOutput,
} from "./commands/SetPrincipalTagAttributeMapCommand";
import {
  TagResourceCommandInput,
  TagResourceCommandOutput,
} from "./commands/TagResourceCommand";
import {
  UnlinkDeveloperIdentityCommandInput,
  UnlinkDeveloperIdentityCommandOutput,
} from "./commands/UnlinkDeveloperIdentityCommand";
import {
  UnlinkIdentityCommandInput,
  UnlinkIdentityCommandOutput,
} from "./commands/UnlinkIdentityCommand";
import {
  UntagResourceCommandInput,
  UntagResourceCommandOutput,
} from "./commands/UntagResourceCommand";
import {
  UpdateIdentityPoolCommandInput,
  UpdateIdentityPoolCommandOutput,
} from "./commands/UpdateIdentityPoolCommand";
export interface CognitoIdentity {
  createIdentityPool(
    args: CreateIdentityPoolCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateIdentityPoolCommandOutput>;
  createIdentityPool(
    args: CreateIdentityPoolCommandInput,
    cb: (err: any, data?: CreateIdentityPoolCommandOutput) => void
  ): void;
  createIdentityPool(
    args: CreateIdentityPoolCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateIdentityPoolCommandOutput) => void
  ): void;
  deleteIdentities(
    args: DeleteIdentitiesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteIdentitiesCommandOutput>;
  deleteIdentities(
    args: DeleteIdentitiesCommandInput,
    cb: (err: any, data?: DeleteIdentitiesCommandOutput) => void
  ): void;
  deleteIdentities(
    args: DeleteIdentitiesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteIdentitiesCommandOutput) => void
  ): void;
  deleteIdentityPool(
    args: DeleteIdentityPoolCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteIdentityPoolCommandOutput>;
  deleteIdentityPool(
    args: DeleteIdentityPoolCommandInput,
    cb: (err: any, data?: DeleteIdentityPoolCommandOutput) => void
  ): void;
  deleteIdentityPool(
    args: DeleteIdentityPoolCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteIdentityPoolCommandOutput) => void
  ): void;
  describeIdentity(
    args: DescribeIdentityCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeIdentityCommandOutput>;
  describeIdentity(
    args: DescribeIdentityCommandInput,
    cb: (err: any, data?: DescribeIdentityCommandOutput) => void
  ): void;
  describeIdentity(
    args: DescribeIdentityCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeIdentityCommandOutput) => void
  ): void;
  describeIdentityPool(
    args: DescribeIdentityPoolCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DescribeIdentityPoolCommandOutput>;
  describeIdentityPool(
    args: DescribeIdentityPoolCommandInput,
    cb: (err: any, data?: DescribeIdentityPoolCommandOutput) => void
  ): void;
  describeIdentityPool(
    args: DescribeIdentityPoolCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DescribeIdentityPoolCommandOutput) => void
  ): void;
  getCredentialsForIdentity(
    args: GetCredentialsForIdentityCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetCredentialsForIdentityCommandOutput>;
  getCredentialsForIdentity(
    args: GetCredentialsForIdentityCommandInput,
    cb: (err: any, data?: GetCredentialsForIdentityCommandOutput) => void
  ): void;
  getCredentialsForIdentity(
    args: GetCredentialsForIdentityCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetCredentialsForIdentityCommandOutput) => void
  ): void;
  getId(
    args: GetIdCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetIdCommandOutput>;
  getId(
    args: GetIdCommandInput,
    cb: (err: any, data?: GetIdCommandOutput) => void
  ): void;
  getId(
    args: GetIdCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetIdCommandOutput) => void
  ): void;
  getIdentityPoolRoles(
    args: GetIdentityPoolRolesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetIdentityPoolRolesCommandOutput>;
  getIdentityPoolRoles(
    args: GetIdentityPoolRolesCommandInput,
    cb: (err: any, data?: GetIdentityPoolRolesCommandOutput) => void
  ): void;
  getIdentityPoolRoles(
    args: GetIdentityPoolRolesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetIdentityPoolRolesCommandOutput) => void
  ): void;
  getOpenIdToken(
    args: GetOpenIdTokenCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetOpenIdTokenCommandOutput>;
  getOpenIdToken(
    args: GetOpenIdTokenCommandInput,
    cb: (err: any, data?: GetOpenIdTokenCommandOutput) => void
  ): void;
  getOpenIdToken(
    args: GetOpenIdTokenCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetOpenIdTokenCommandOutput) => void
  ): void;
  getOpenIdTokenForDeveloperIdentity(
    args: GetOpenIdTokenForDeveloperIdentityCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetOpenIdTokenForDeveloperIdentityCommandOutput>;
  getOpenIdTokenForDeveloperIdentity(
    args: GetOpenIdTokenForDeveloperIdentityCommandInput,
    cb: (
      err: any,
      data?: GetOpenIdTokenForDeveloperIdentityCommandOutput
    ) => void
  ): void;
  getOpenIdTokenForDeveloperIdentity(
    args: GetOpenIdTokenForDeveloperIdentityCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: GetOpenIdTokenForDeveloperIdentityCommandOutput
    ) => void
  ): void;
  getPrincipalTagAttributeMap(
    args: GetPrincipalTagAttributeMapCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetPrincipalTagAttributeMapCommandOutput>;
  getPrincipalTagAttributeMap(
    args: GetPrincipalTagAttributeMapCommandInput,
    cb: (err: any, data?: GetPrincipalTagAttributeMapCommandOutput) => void
  ): void;
  getPrincipalTagAttributeMap(
    args: GetPrincipalTagAttributeMapCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetPrincipalTagAttributeMapCommandOutput) => void
  ): void;
  listIdentities(
    args: ListIdentitiesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListIdentitiesCommandOutput>;
  listIdentities(
    args: ListIdentitiesCommandInput,
    cb: (err: any, data?: ListIdentitiesCommandOutput) => void
  ): void;
  listIdentities(
    args: ListIdentitiesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListIdentitiesCommandOutput) => void
  ): void;
  listIdentityPools(
    args: ListIdentityPoolsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListIdentityPoolsCommandOutput>;
  listIdentityPools(
    args: ListIdentityPoolsCommandInput,
    cb: (err: any, data?: ListIdentityPoolsCommandOutput) => void
  ): void;
  listIdentityPools(
    args: ListIdentityPoolsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListIdentityPoolsCommandOutput) => void
  ): void;
  listTagsForResource(
    args: ListTagsForResourceCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListTagsForResourceCommandOutput>;
  listTagsForResource(
    args: ListTagsForResourceCommandInput,
    cb: (err: any, data?: ListTagsForResourceCommandOutput) => void
  ): void;
  listTagsForResource(
    args: ListTagsForResourceCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListTagsForResourceCommandOutput) => void
  ): void;
  lookupDeveloperIdentity(
    args: LookupDeveloperIdentityCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<LookupDeveloperIdentityCommandOutput>;
  lookupDeveloperIdentity(
    args: LookupDeveloperIdentityCommandInput,
    cb: (err: any, data?: LookupDeveloperIdentityCommandOutput) => void
  ): void;
  lookupDeveloperIdentity(
    args: LookupDeveloperIdentityCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: LookupDeveloperIdentityCommandOutput) => void
  ): void;
  mergeDeveloperIdentities(
    args: MergeDeveloperIdentitiesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<MergeDeveloperIdentitiesCommandOutput>;
  mergeDeveloperIdentities(
    args: MergeDeveloperIdentitiesCommandInput,
    cb: (err: any, data?: MergeDeveloperIdentitiesCommandOutput) => void
  ): void;
  mergeDeveloperIdentities(
    args: MergeDeveloperIdentitiesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: MergeDeveloperIdentitiesCommandOutput) => void
  ): void;
  setIdentityPoolRoles(
    args: SetIdentityPoolRolesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<SetIdentityPoolRolesCommandOutput>;
  setIdentityPoolRoles(
    args: SetIdentityPoolRolesCommandInput,
    cb: (err: any, data?: SetIdentityPoolRolesCommandOutput) => void
  ): void;
  setIdentityPoolRoles(
    args: SetIdentityPoolRolesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: SetIdentityPoolRolesCommandOutput) => void
  ): void;
  setPrincipalTagAttributeMap(
    args: SetPrincipalTagAttributeMapCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<SetPrincipalTagAttributeMapCommandOutput>;
  setPrincipalTagAttributeMap(
    args: SetPrincipalTagAttributeMapCommandInput,
    cb: (err: any, data?: SetPrincipalTagAttributeMapCommandOutput) => void
  ): void;
  setPrincipalTagAttributeMap(
    args: SetPrincipalTagAttributeMapCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: SetPrincipalTagAttributeMapCommandOutput) => void
  ): void;
  tagResource(
    args: TagResourceCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<TagResourceCommandOutput>;
  tagResource(
    args: TagResourceCommandInput,
    cb: (err: any, data?: TagResourceCommandOutput) => void
  ): void;
  tagResource(
    args: TagResourceCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: TagResourceCommandOutput) => void
  ): void;
  unlinkDeveloperIdentity(
    args: UnlinkDeveloperIdentityCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UnlinkDeveloperIdentityCommandOutput>;
  unlinkDeveloperIdentity(
    args: UnlinkDeveloperIdentityCommandInput,
    cb: (err: any, data?: UnlinkDeveloperIdentityCommandOutput) => void
  ): void;
  unlinkDeveloperIdentity(
    args: UnlinkDeveloperIdentityCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UnlinkDeveloperIdentityCommandOutput) => void
  ): void;
  unlinkIdentity(
    args: UnlinkIdentityCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UnlinkIdentityCommandOutput>;
  unlinkIdentity(
    args: UnlinkIdentityCommandInput,
    cb: (err: any, data?: UnlinkIdentityCommandOutput) => void
  ): void;
  unlinkIdentity(
    args: UnlinkIdentityCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UnlinkIdentityCommandOutput) => void
  ): void;
  untagResource(
    args: UntagResourceCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UntagResourceCommandOutput>;
  untagResource(
    args: UntagResourceCommandInput,
    cb: (err: any, data?: UntagResourceCommandOutput) => void
  ): void;
  untagResource(
    args: UntagResourceCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UntagResourceCommandOutput) => void
  ): void;
  updateIdentityPool(
    args: UpdateIdentityPoolCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateIdentityPoolCommandOutput>;
  updateIdentityPool(
    args: UpdateIdentityPoolCommandInput,
    cb: (err: any, data?: UpdateIdentityPoolCommandOutput) => void
  ): void;
  updateIdentityPool(
    args: UpdateIdentityPoolCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateIdentityPoolCommandOutput) => void
  ): void;
}
export declare class CognitoIdentity
  extends CognitoIdentityClient
  implements CognitoIdentity {}
