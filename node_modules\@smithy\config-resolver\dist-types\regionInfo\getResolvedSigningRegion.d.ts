/**
 * @internal
 * @deprecated unused for endpointRuleSets.
 */
export interface GetResolvedSigningRegionOptions {
    regionRegex: string;
    signingRegion?: string;
    useFipsEndpoint: boolean;
}
/**
 * @internal
 * @deprecated unused for endpointRuleSets.
 */
export declare const getResolvedSigningRegion: (hostname: string, { signingRegion, regionRegex, useFipsEndpoint }: GetResolvedSigningRegionOptions) => string | undefined;
