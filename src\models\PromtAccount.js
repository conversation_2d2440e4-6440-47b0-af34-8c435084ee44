const mongoose = require('mongoose');

// <PERSON><PERSON> - matches the JSON structure from browser export
const cookieSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        max: 255
    },
    value: {
        type: String,
        required: true,
        max: 2048 // Standard browser cookie value limit
    },
    domain: {
        type: String,
        required: true,
        max: 255
    },
    path: {
        type: String,
        required: true,
        max: 255,
        default: '/'
    },
    expires: {
        type: Number, // Unix timestamp
        required: false
    },
    size: {
        type: Number,
        required: false
    },
    httpOnly: {
        type: Boolean,
        default: false
    },
    secure: {
        type: Boolean,
        default: false
    },
    session: {
        type: Boolean,
        default: false
    },
    sameSite: {
        type: String,
        enum: ['Strict', 'Lax', 'None'],
        default: 'Lax'
    },
    priority: {
        type: String,
        enum: ['Low', 'Medium', 'High'],
        default: 'Medium'
    },
    sameParty: {
        type: Boolean,
        default: false
    },
    sourceScheme: {
        type: String,
        enum: ['Secure', 'NonSecure'],
        default: 'Secure'
    }
}, { _id: false }); // Don't create separate _id for cookie subdocuments

// Main PromtAccount Schema
const promtAccountSchema = new mongoose.Schema({    
    accountName: {
        type: String,
        required: true,
        max: 255,
        trim: true
    },
    email: {
        type: String,
        required: true,
        max: 255,
        trim: true,
        lowercase: true,
        validate: {
            validator: function(email) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            },
            message: 'Please enter a valid email address'
        }
    },
    cookies: {
        type: [cookieSchema],
        default: []
    },
    userAgent: {
        type: String,
        required: true,
        max: 1024
    },
    timestamp: {
        type: Date,
        required: true,
        default: Date.now
    },
    // Additional management fields
    availableTokens: {
        type: Number,
        default: 0,
        min: 0,
        max: 1000000
    },
    isLocked: {
        type: Boolean,
        default: false
    },
    lockedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        default: null
    },
    url: {
        type: String,
        max: 2048,
        default: ''
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastUsed: {
        type: Date,
        default: Date.now
    },
    notes: {
        type: String,
        max: 1024,
        default: ''
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Indexes for better query performance
promtAccountSchema.index({ userId: 1, isActive: 1 });
promtAccountSchema.index({ email: 1 });
promtAccountSchema.index({ lastUsed: -1 });
promtAccountSchema.index({ isLocked: 1 });
promtAccountSchema.index({ availableTokens: 1 });

// Update the updatedAt field before saving
promtAccountSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Update the updatedAt field before updating
promtAccountSchema.pre('findOneAndUpdate', function(next) {
    this.set({ updatedAt: Date.now() });
    next();
});

module.exports = mongoose.model('PromtAccount', promtAccountSchema);
