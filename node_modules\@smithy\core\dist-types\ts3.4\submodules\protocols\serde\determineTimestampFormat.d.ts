import { NormalizedSchema } from "@smithy/core/schema";
import { TimestampDateTimeSchema, TimestampEpochSecondsSchema, TimestampHttpDateSchema, CodecSettings } from "@smithy/types";
/**
 * Assuming the schema is a timestamp type, the function resolves the format using
 * either the timestamp's own traits, or the default timestamp format from the CodecSettings.
 *
 * @internal
 */
export declare function determineTimestampFormat(ns: NormalizedSchema, settings: CodecSettings): TimestampDateTimeSchema | TimestampHttpDateSchema | TimestampEpochSecondsSchema;
