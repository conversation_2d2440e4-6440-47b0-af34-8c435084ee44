import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CognitoIdentityClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CognitoIdentityClient";
import {
  MergeDeveloperIdentitiesInput,
  MergeDeveloperIdentitiesResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface MergeDeveloperIdentitiesCommandInput
  extends MergeDeveloperIdentitiesInput {}
export interface MergeDeveloperIdentitiesCommandOutput
  extends MergeDeveloperIdentitiesResponse,
    __MetadataBearer {}
declare const MergeDeveloperIdentitiesCommand_base: {
  new (
    input: MergeDeveloperIdentitiesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    MergeDeveloperIdentitiesCommandInput,
    MergeDeveloperIdentitiesCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: MergeDeveloperIdentitiesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    MergeDeveloperIdentitiesCommandInput,
    MergeDeveloperIdentitiesCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class MergeDeveloperIdentitiesCommand extends MergeDeveloperIdentitiesCommand_base {
  protected static __types: {
    api: {
      input: MergeDeveloperIdentitiesInput;
      output: MergeDeveloperIdentitiesResponse;
    };
    sdk: {
      input: MergeDeveloperIdentitiesCommandInput;
      output: MergeDeveloperIdentitiesCommandOutput;
    };
  };
}
