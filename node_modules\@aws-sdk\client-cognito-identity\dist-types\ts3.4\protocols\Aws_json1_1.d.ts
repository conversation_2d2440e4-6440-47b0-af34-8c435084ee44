import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import {
  CreateIdentityPoolCommandInput,
  CreateIdentityPoolCommandOutput,
} from "../commands/CreateIdentityPoolCommand";
import {
  DeleteIdentitiesCommandInput,
  DeleteIdentitiesCommandOutput,
} from "../commands/DeleteIdentitiesCommand";
import {
  DeleteIdentityPoolCommandInput,
  DeleteIdentityPoolCommandOutput,
} from "../commands/DeleteIdentityPoolCommand";
import {
  DescribeIdentityCommandInput,
  DescribeIdentityCommandOutput,
} from "../commands/DescribeIdentityCommand";
import {
  DescribeIdentityPoolCommandInput,
  DescribeIdentityPoolCommandOutput,
} from "../commands/DescribeIdentityPoolCommand";
import {
  GetCredentialsForIdentityCommandInput,
  GetCredentialsForIdentityCommandOutput,
} from "../commands/GetCredentialsForIdentityCommand";
import {
  GetIdCommandInput,
  GetIdCommandOutput,
} from "../commands/GetIdCommand";
import {
  GetIdentityPoolRolesCommandInput,
  GetIdentityPoolRolesCommandOutput,
} from "../commands/GetIdentityPoolRolesCommand";
import {
  GetOpenIdTokenCommandInput,
  GetOpenIdTokenCommandOutput,
} from "../commands/GetOpenIdTokenCommand";
import {
  GetOpenIdTokenForDeveloperIdentityCommandInput,
  GetOpenIdTokenForDeveloperIdentityCommandOutput,
} from "../commands/GetOpenIdTokenForDeveloperIdentityCommand";
import {
  GetPrincipalTagAttributeMapCommandInput,
  GetPrincipalTagAttributeMapCommandOutput,
} from "../commands/GetPrincipalTagAttributeMapCommand";
import {
  ListIdentitiesCommandInput,
  ListIdentitiesCommandOutput,
} from "../commands/ListIdentitiesCommand";
import {
  ListIdentityPoolsCommandInput,
  ListIdentityPoolsCommandOutput,
} from "../commands/ListIdentityPoolsCommand";
import {
  ListTagsForResourceCommandInput,
  ListTagsForResourceCommandOutput,
} from "../commands/ListTagsForResourceCommand";
import {
  LookupDeveloperIdentityCommandInput,
  LookupDeveloperIdentityCommandOutput,
} from "../commands/LookupDeveloperIdentityCommand";
import {
  MergeDeveloperIdentitiesCommandInput,
  MergeDeveloperIdentitiesCommandOutput,
} from "../commands/MergeDeveloperIdentitiesCommand";
import {
  SetIdentityPoolRolesCommandInput,
  SetIdentityPoolRolesCommandOutput,
} from "../commands/SetIdentityPoolRolesCommand";
import {
  SetPrincipalTagAttributeMapCommandInput,
  SetPrincipalTagAttributeMapCommandOutput,
} from "../commands/SetPrincipalTagAttributeMapCommand";
import {
  TagResourceCommandInput,
  TagResourceCommandOutput,
} from "../commands/TagResourceCommand";
import {
  UnlinkDeveloperIdentityCommandInput,
  UnlinkDeveloperIdentityCommandOutput,
} from "../commands/UnlinkDeveloperIdentityCommand";
import {
  UnlinkIdentityCommandInput,
  UnlinkIdentityCommandOutput,
} from "../commands/UnlinkIdentityCommand";
import {
  UntagResourceCommandInput,
  UntagResourceCommandOutput,
} from "../commands/UntagResourceCommand";
import {
  UpdateIdentityPoolCommandInput,
  UpdateIdentityPoolCommandOutput,
} from "../commands/UpdateIdentityPoolCommand";
export declare const se_CreateIdentityPoolCommand: (
  input: CreateIdentityPoolCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteIdentitiesCommand: (
  input: DeleteIdentitiesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteIdentityPoolCommand: (
  input: DeleteIdentityPoolCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeIdentityCommand: (
  input: DescribeIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeIdentityPoolCommand: (
  input: DescribeIdentityPoolCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetCredentialsForIdentityCommand: (
  input: GetCredentialsForIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetIdCommand: (
  input: GetIdCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetIdentityPoolRolesCommand: (
  input: GetIdentityPoolRolesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetOpenIdTokenCommand: (
  input: GetOpenIdTokenCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetOpenIdTokenForDeveloperIdentityCommand: (
  input: GetOpenIdTokenForDeveloperIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetPrincipalTagAttributeMapCommand: (
  input: GetPrincipalTagAttributeMapCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListIdentitiesCommand: (
  input: ListIdentitiesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListIdentityPoolsCommand: (
  input: ListIdentityPoolsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTagsForResourceCommand: (
  input: ListTagsForResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_LookupDeveloperIdentityCommand: (
  input: LookupDeveloperIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_MergeDeveloperIdentitiesCommand: (
  input: MergeDeveloperIdentitiesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_SetIdentityPoolRolesCommand: (
  input: SetIdentityPoolRolesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_SetPrincipalTagAttributeMapCommand: (
  input: SetPrincipalTagAttributeMapCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_TagResourceCommand: (
  input: TagResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UnlinkDeveloperIdentityCommand: (
  input: UnlinkDeveloperIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UnlinkIdentityCommand: (
  input: UnlinkIdentityCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UntagResourceCommand: (
  input: UntagResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateIdentityPoolCommand: (
  input: UpdateIdentityPoolCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_CreateIdentityPoolCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateIdentityPoolCommandOutput>;
export declare const de_DeleteIdentitiesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteIdentitiesCommandOutput>;
export declare const de_DeleteIdentityPoolCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteIdentityPoolCommandOutput>;
export declare const de_DescribeIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeIdentityCommandOutput>;
export declare const de_DescribeIdentityPoolCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeIdentityPoolCommandOutput>;
export declare const de_GetCredentialsForIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetCredentialsForIdentityCommandOutput>;
export declare const de_GetIdCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetIdCommandOutput>;
export declare const de_GetIdentityPoolRolesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetIdentityPoolRolesCommandOutput>;
export declare const de_GetOpenIdTokenCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetOpenIdTokenCommandOutput>;
export declare const de_GetOpenIdTokenForDeveloperIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetOpenIdTokenForDeveloperIdentityCommandOutput>;
export declare const de_GetPrincipalTagAttributeMapCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetPrincipalTagAttributeMapCommandOutput>;
export declare const de_ListIdentitiesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListIdentitiesCommandOutput>;
export declare const de_ListIdentityPoolsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListIdentityPoolsCommandOutput>;
export declare const de_ListTagsForResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTagsForResourceCommandOutput>;
export declare const de_LookupDeveloperIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<LookupDeveloperIdentityCommandOutput>;
export declare const de_MergeDeveloperIdentitiesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<MergeDeveloperIdentitiesCommandOutput>;
export declare const de_SetIdentityPoolRolesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<SetIdentityPoolRolesCommandOutput>;
export declare const de_SetPrincipalTagAttributeMapCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<SetPrincipalTagAttributeMapCommandOutput>;
export declare const de_TagResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<TagResourceCommandOutput>;
export declare const de_UnlinkDeveloperIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UnlinkDeveloperIdentityCommandOutput>;
export declare const de_UnlinkIdentityCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UnlinkIdentityCommandOutput>;
export declare const de_UntagResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UntagResourceCommandOutput>;
export declare const de_UpdateIdentityPoolCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateIdentityPoolCommandOutput>;
