const PromtAccount = require('../models/PromtAccount');
const User = require('../models/User');
const mongoose = require('mongoose');

class PromtAccountController {
  // Get new worker account (not locked with available tokens)
  async getNewWorker(req, res) {
    try {
      const minTokens = req.query.minTokens || 1;
      
      // Find account that is not locked and has available tokens
      const account = await PromtAccount.findOneAndUpdate(
        { 
          isLocked: false,
          availableTokens: { $gte: minTokens }
        },
        { 
          isLocked: true,
          lastUsed: Date.now()
        },
        { 
          new: true,
          sort: { lastUsed: 1 } // Get least recently used account first
        }
      );
      
      if (!account) {
        return res.status(404).json({ 
          success: false,
          message: 'No available accounts found' 
        });
      }
      
      res.status(200).json({
        success: true,
        message: 'Worker account retrieved successfully',
        data: { account }
      });
    } catch (error) {
      console.error('Get new worker error:', error);
      res.status(500).json({ 
        success: false,
        message: 'Failed to get worker account',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Get all user's promt accounts
  async getAllAccounts(req, res) {
    try {
      const accounts = await PromtAccount.find({ 
        isActive: true 
      }).sort({ lastUsed: -1 });
      
      res.status(200).json({
        success: true,
        message: 'Accounts retrieved successfully',
        data: { accounts }
      });
    } catch (error) {
      console.error('Get all accounts error:', error);
      res.status(500).json({ 
        success: false,
        message: 'Failed to retrieve accounts',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Get specific promt account
  async getAccount(req, res) {
    try {
      const account = await PromtAccount.findOne({
        _id: req.params.accountId,
        isActive: true
      });
      
      if (!account) {
        return res.status(404).json({ 
          success: false,
          message: 'Account not found' 
        });
      }
      
      res.status(200).json({
        success: true,
        message: 'Account retrieved successfully',
        data: { account }
      });
    } catch (error) {
      console.error('Get account error:', error);
      res.status(500).json({ 
        success: false,
        message: 'Failed to retrieve account',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Create new promt account
  async createAccount(req, res) {
    try {
      // Check if account with this email already exists
      const existingAccount = await PromtAccount.findOne({
        email: req.body.email,
        isActive: true
      });
      
      if (existingAccount) {
        return res.status(400).json({ 
          success: false,
          message: 'Account with this email already exists' 
        });
      }
      
      const account = new PromtAccount({
        accountName: req.body.accountName,
        email: req.body.email,
        availableTokens: req.body.availableTokens || 0,
        cookies: req.body.cookies || [],
        url: req.body.url,
        userAgent: req.body.userAgent,
        notes: req.body.notes || ''
      });
      
      const savedAccount = await account.save();
      
      res.status(201).json({
        success: true,
        message: 'Account created successfully',
        data: { account: savedAccount }
      });
    } catch (error) {
      console.error('Create account error:', error);
      res.status(400).json({ 
        success: false,
        message: 'Failed to create account',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Invalid account data'
      });
    }
  }

  // Update promt account
  async updateAccount(req, res) {
    try {
      const account = await PromtAccount.findOne({
        _id: req.params.accountId,
        isActive: true
      });
      
      if (!account) {
        return res.status(404).json({ 
          success: false,
          message: 'Account not found' 
        });
      }
      
      // Update only provided fields
      const updateData = {};
      if (req.body.accountName) updateData.accountName = req.body.accountName;
      if (req.body.email) updateData.email = req.body.email;
      if (req.body.availableTokens !== undefined) updateData.availableTokens = req.body.availableTokens;
      if (req.body.cookies) updateData.cookies = req.body.cookies;
      if (req.body.url) updateData.url = req.body.url;
      if (req.body.userAgent) updateData.userAgent = req.body.userAgent;
      if (req.body.notes !== undefined) updateData.notes = req.body.notes;
      
      const updatedAccount = await PromtAccount.findOneAndUpdate(
        { _id: req.params.accountId },
        updateData,
        { new: true, runValidators: true }
      );
      
      res.status(200).json({
        success: true,
        message: 'Account updated successfully',
        data: { account: updatedAccount }
      });
    } catch (error) {
      console.error('Update account error:', error);
      res.status(400).json({ 
        success: false,
        message: 'Failed to update account',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Invalid account data'
      });
    }
  }

  // Delete promt account (soft)
  async deleteAccount(req, res) {
    try {
      const result = await PromtAccount.findOneAndUpdate(
        { _id: req.params.accountId },
        { isActive: false },
        { new: true }
      );
      
      if (!result) {
        return res.status(404).json({ 
          success: false,
          message: 'Account not found' 
        });
      }
      
      res.status(200).json({ 
        success: true,
        message: 'Account deleted successfully' 
      });
    } catch (error) {
      console.error('Delete account error:', error);
      res.status(500).json({ 
        success: false,
        message: 'Failed to delete account',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Lock account for exclusive use
  async lockAccount(req, res) {
    try {
      const result = await PromtAccount.findOneAndUpdate(
        { _id: req.params.accountId, isLocked: false },
        { 
          isLocked: true,
          lockedBy: req.user.userId
        },
        { new: true }
      );
      
      if (!result) {
        return res.status(400).json({ 
          success: false,
          message: 'Account not found or already locked' 
        });
      }
      
      res.status(200).json({
        success: true,
        message: 'Account locked successfully',
        data: { account: result }
      });
    } catch (error) {
      console.error('Lock account error:', error);
      res.status(500).json({ 
        success: false,
        message: 'Failed to lock account',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Unlock account
  async unlockAccount(req, res) {
    try {
      const result = await PromtAccount.findOneAndUpdate(
        { _id: req.params.accountId, isLocked: true },
        { 
          isLocked: false,
          lockedBy: null
        },
        { new: true }
      );
      
      if (!result) {
        return res.status(400).json({ 
          success: false,
          message: 'Account not found or not locked' 
        });
      }
      
      res.status(200).json({
        success: true,
        message: 'Account unlocked successfully',
        data: { account: result }
      });
    } catch (error) {
      console.error('Unlock account error:', error);
      res.status(500).json({ 
        success: false,
        message: 'Failed to unlock account',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Set account tokens
  async setTokens(req, res) {
    try {
      if (!req.body.amount || req.body.amount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Valid amount required'
        });
      }

      const account = await PromtAccount.findOne({
        _id: req.params.accountId,
        isActive: true
      });

      if (!account) {
        return res.status(404).json({
          success: false,
          message: 'Account not found'
        });
      }

      const diff = Math.abs(req.body.amount - account.availableTokens);

      // Note: User token management would need to be implemented based on your User model
      // const user = await User.findOneAndUpdate(
      //   { _id: req.user.userId },
      //   { $inc: { tokens: -diff } },
      //   { new: true }
      // );

      if (req.body.amount < 0) {
        account.availableTokens = 0;
      } else {
        account.availableTokens = req.body.amount;
      }

      await account.save();

      res.status(200).json({
        success: true,
        message: 'Account tokens updated successfully',
        data: { account }
      });
    } catch (error) {
      console.error('Set tokens error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to set account tokens',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
}

module.exports = new PromtAccountController();
