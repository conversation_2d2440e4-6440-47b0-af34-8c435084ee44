const express = require('express');
const { body, param, query } = require('express-validator');
const promtAccountController = require('../controllers/promtAccountController');
const { authenticate, authorize, trackActivity } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Validation rules
const createAccountValidation = [
  body('accountName')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Account name is required and must be between 1 and 255 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('userAgent')
    .trim()
    .isLength({ min: 1, max: 1024 })
    .withMessage('User agent is required and must be between 1 and 1024 characters'),
  body('availableTokens')
    .optional()
    .isInt({ min: 0, max: 1000000 })
    .withMessage('Available tokens must be between 0 and 1,000,000'),
  body('url')
    .optional()
    .isLength({ max: 2048 })
    .withMessage('URL must not exceed 2048 characters'),
  body('notes')
    .optional()
    .isLength({ max: 1024 })
    .withMessage('Notes must not exceed 1024 characters'),
  body('cookies')
    .optional()
    .isArray()
    .withMessage('Cookies must be an array')
];

const updateAccountValidation = [
  body('accountName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Account name must be between 1 and 255 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('userAgent')
    .optional()
    .trim()
    .isLength({ min: 1, max: 1024 })
    .withMessage('User agent must be between 1 and 1024 characters'),
  body('availableTokens')
    .optional()
    .isInt({ min: 0, max: 1000000 })
    .withMessage('Available tokens must be between 0 and 1,000,000'),
  body('url')
    .optional()
    .isLength({ max: 2048 })
    .withMessage('URL must not exceed 2048 characters'),
  body('notes')
    .optional()
    .isLength({ max: 1024 })
    .withMessage('Notes must not exceed 1024 characters'),
  body('cookies')
    .optional()
    .isArray()
    .withMessage('Cookies must be an array')
];

const setTokensValidation = [
  body('amount')
    .isInt({ min: 0 })
    .withMessage('Amount must be a positive integer')
];

const accountIdValidation = [
  param('accountId')
    .isMongoId()
    .withMessage('Invalid account ID')
];

const newWorkerValidation = [
  query('minTokens')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Minimum tokens must be a positive integer')
];

// Routes

/**
 * @route   GET /api/promt-accounts/new-worker
 * @desc    Get new worker account (not locked with available tokens)
 * @access  Public
 */
router.get('/new-worker', newWorkerValidation, validateRequest, promtAccountController.getNewWorker);

/**
 * @route   GET /api/promt-accounts
 * @desc    Get all promt accounts
 * @access  Private
 */
router.get('/', authenticate, trackActivity, promtAccountController.getAllAccounts);

/**
 * @route   GET /api/promt-accounts/:accountId
 * @desc    Get specific promt account
 * @access  Private
 */
router.get('/:accountId', authenticate, trackActivity, accountIdValidation, validateRequest, promtAccountController.getAccount);

/**
 * @route   POST /api/promt-accounts
 * @desc    Create new promt account
 * @access  Private
 */
router.post('/', authenticate, trackActivity, createAccountValidation, validateRequest, promtAccountController.createAccount);

/**
 * @route   PUT /api/promt-accounts/:accountId
 * @desc    Update promt account
 * @access  Private
 */
router.put('/:accountId', authenticate, trackActivity, accountIdValidation, updateAccountValidation, validateRequest, promtAccountController.updateAccount);

/**
 * @route   DELETE /api/promt-accounts/:accountId
 * @desc    Delete promt account (soft delete)
 * @access  Private
 */
router.delete('/:accountId', authenticate, trackActivity, accountIdValidation, validateRequest, promtAccountController.deleteAccount);

/**
 * @route   PUT /api/promt-accounts/:accountId/lock
 * @desc    Lock account for exclusive use
 * @access  Private
 */
router.put('/:accountId/lock', authenticate, trackActivity, accountIdValidation, validateRequest, promtAccountController.lockAccount);

/**
 * @route   PUT /api/promt-accounts/:accountId/unlock
 * @desc    Unlock account
 * @access  Private
 */
router.put('/:accountId/unlock', authenticate, trackActivity, accountIdValidation, validateRequest, promtAccountController.unlockAccount);

/**
 * @route   PUT /api/promt-accounts/:accountId/tokens
 * @desc    Set account tokens
 * @access  Private
 */
router.put('/:accountId/tokens', authenticate, trackActivity, accountIdValidation, setTokensValidation, validateRequest, promtAccountController.setTokens);

module.exports = router;
